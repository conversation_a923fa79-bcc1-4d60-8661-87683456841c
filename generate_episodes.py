# generate_episodes.py
"""
剧本生成模块

主要功能：
1. 从章节摘要生成分集剧本
2. 支持两种剧本转换方式：
   - 代码转换（默认）：快速、稳定、可控
   - LLM转换：更智能但可能不稳定

配置说明：
- USE_CODE_CONVERSION = True: 使用代码转换（推荐）
- USE_CODE_CONVERSION = False: 使用LLM转换

代码转换支持的剧本格式：
- 场景标记：场景1、SCENE 1、Scene 1
- 环境描述：环境：xxx、背景：xxx
- 角色对话：角色名：对话内容
- 旁白：[旁白内容]、（旁白内容）、旁白：内容

使用方法：
python generate_episodes.py input_file.json [--output_dir output] [--style engaging] [--language Chinese]
"""
import sys
import os
import re
import argparse
from typing import List, Dict, Any, Optional, Tuple, Union
from tqdm import tqdm
import tiktoken
from config import (
    logger,
    get_param,
    ANIMATION_OUTPUTS_DIR,
    ANIMATION_EPISODES_DIR,
    ANIMATION_TEMP_DIR,
    ANIMATION_DRAMA_DIR,
    LANGUAGE_CODES
)
from modules.langchain_interface import call_llm_json_response, call_llm

from modules.embedding_management import (
    EmbeddingManager,
    get_embedding_manager,
    get_embeddings,
    get_text_hash,
    Episode
)
from modules.gpt_parameters import (
    LLM_PROVIDER_AZURE,
    LLM_PROVIDER_OPENAI,
    LLM_PROVIDER_ANTHROPIC,
    LLM_PROVIDER_DEEPSEEK,
    MODEL_KEY_GPT41,
    MODEL_KEY_O3_MINI,
    MODEL_KEY_DEEPSEEK_CHAT,
    MODEL_KEY_DEEPSEEK_REASONER,
    MODEL_KEY_ARK_DEEPSEEK_R1,
    load_model_settings,
    DEFAULT_LLM_TYPE,
    DEFAULT_MODEL_KEY
)
from modules.text_preprocessing import split_text_for_analysis
from modules.utils import smart_paragraph_split, read_file_with_encoding, calculate_token_count
import unicodedata
import numpy as np
import json
import time
import yaml
from modules.utils import write_temp_file
from modules.episode_outline import (
    generate_global_outline,
    group_chapter_summaries,
    summarize_groups
)

# Constants
DEBUG_MODE = True  # 新增调试变量
DEBUG_MAX_EPISODES = 3  # 调试模式下最大生成集数
CHAPTER_SUMMARY_MAX_LENGTH = 1000
EPISODE_SCRIPT_MIN_WORDS = 1000
EPISODE_SCRIPT_MAX_WORDS = 1500
PROGRESS_SAVE_INTERVAL_CHAPTERS = 5
TARGET_WORDS_PER_EPISODE = 1200
ENABLE_SCRIPT_CACHE = False
EPISODE_DEBUG = True
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数

# Script conversion method configuration
USE_CODE_CONVERSION = True  # True: 使用代码转换, False: 使用LLM转换

# 新增参考资料配置
INSPIRATIONAL_REFERENCES = {
    "inspirational_references": {
        "audio_dramas": [
            {
                "title": "The Black Tapes",
                "style_notes": "A journalist investigates unsolved paranormal mysteries, blending suspense with skepticism."
            },
            {
                "title": "Welcome to Night Vale",
                "style_notes": "Surreal storytelling with rich character psychology and atmospheric world-building."
            },
            {
                "title": "The Magnus Archives",
                "style_notes": "Deep character development with psychological horror and interconnected narratives."
            }
        ],
        "authors": [
            {
                "name": "Stephen King",
                "style_notes": "Deep character exploration, building terror through ordinary settings turned sinister."
            },
            {
                "name": "Margaret Atwood",
                "style_notes": "Complex character psychology with rich internal monologue and social commentary."
            },
            {
                "name": "Neil Gaiman",
                "style_notes": "Mythological storytelling with profound character transformation and emotional depth."
            }
        ],
        "stylistic_elements": [
            "First-person narration to create intimacy and psychological connection.",
            "Slow-building tension leading to powerful emotional climaxes.",
            "Use of symbolism and metaphor to add deeper meaning to character actions.",
            "Internal monologue that reveals character psychology and motivations.",
            "Realistic dialogue with subtext that reflects character relationships.",
            "Environmental storytelling where setting reflects character emotional states."
        ]
    }
}

# 改进的脚本质量控制参数
EPISODE_QUALITY_STANDARDS = {
    "min_character_depth_score": 0.8,  # 角色深度最小分数
    "min_emotional_impact_score": 0.7,  # 情感影响力最小分数
    "min_dialogue_sophistication": 0.8,  # 对话复杂度最小分数
    "required_psychological_elements": [
        "character_internal_conflict",
        "emotional_subtext",
        "psychological_motivation",
        "character_growth_moment"
    ]
}

# 场景描述增强系统
SCENE_DESCRIPTION_FRAMEWORK = {
    "sensory_layers": {
        "visual_details": "具体的视觉元素，包括光影、色彩、质感",
        "auditory_elements": "声音环境，包括环境音、对话音调、沉默",
        "tactile_sensations": "触感描述，包括温度、湿度、质地",
        "olfactory_cues": "气味描述，用于营造氛围",
        "emotional_atmosphere": "情感氛围，通过环境反映内心状态"
    },
    "cinematographic_techniques": [
        "close_up_details",      # 特写细节
        "wide_establishing_shots", # 全景建立镜头
        "dramatic_lighting",     # 戏剧性光照
        "symbolic_elements",     # 象征性元素
        "rhythm_and_pacing"      # 节奏与节拍
    ],
    "narrative_impact_factors": [
        "foreshadowing_elements",  # 伏笔元素
        "character_revelation",    # 角色揭示
        "tension_building",        # 张力构建
        "emotional_resonance",     # 情感共鸣
        "thematic_reinforcement"   # 主题强化
    ]
}

# 角色心理分析参数
CHARACTER_PSYCHOLOGY_FRAMEWORK = {
    "core_psychological_traits": [
        "internal_motivations",
        "emotional_patterns",
        "fear_responses",
        "desire_drivers",
        "relationship_styles",
        "conflict_resolution_methods"
    ],
    "dialogue_authenticity_factors": [
        "speech_pattern_consistency",
        "emotional_subtext_presence",
        "character_voice_uniqueness",
        "psychological_believability"
    ],
    "psychological_depth_layers": {
        "surface_emotions": "立即可见的情感表现",
        "underlying_motivations": "推动行为的深层动机",
        "unconscious_patterns": "角色未意识到的行为模式",
        "internal_conflicts": "内心的矛盾与挣扎",
        "growth_potential": "角色发展和成长的可能性"
    },
    "emotional_complexity_markers": [
        "contradictory_feelings",
        "suppressed_emotions",
        "masked_vulnerabilities",
        "defensive_mechanisms",
        "hope_vs_despair_balance"
    ]
}

# 加载模型设置
# 可以使用预加载的默认值
LLM_TYPE, MODEL_KEY = DEFAULT_LLM_TYPE, DEFAULT_MODEL_KEY

# 为每个API函数定义LLM类型和模型键
SMART_PHASE_ALLOCATION_LLM_TYPE = LLM_PROVIDER_AZURE
SMART_PHASE_ALLOCATION_MODEL_KEY = MODEL_KEY_O3_MINI

SMART_EPISODE_ALLOCATION_LLM_TYPE = LLM_PROVIDER_AZURE
SMART_EPISODE_ALLOCATION_MODEL_KEY = MODEL_KEY_O3_MINI

GENERATE_EPISODE_STRUCTURE_LLM_TYPE = LLM_PROVIDER_AZURE
GENERATE_EPISODE_STRUCTURE_MODEL_KEY = MODEL_KEY_GPT41

GENERATE_EPISODE_SCRIPT_LLM_TYPE = LLM_PROVIDER_AZURE
GENERATE_EPISODE_SCRIPT_MODEL_KEY = MODEL_KEY_GPT41

REVIEW_SCRIPT_LLM_TYPE = LLM_PROVIDER_AZURE
REVIEW_SCRIPT_MODEL_KEY = MODEL_KEY_GPT41

REFINE_SCRIPT_LLM_TYPE = LLM_PROVIDER_AZURE
REFINE_SCRIPT_MODEL_KEY = MODEL_KEY_GPT41

REVIEW_SCRIPT_STRUCTURE_LLM_TYPE = LLM_PROVIDER_AZURE
REVIEW_SCRIPT_STRUCTURE_MODEL_KEY = MODEL_KEY_GPT41

REFINE_SCRIPT_STRUCTURE_LLM_TYPE = LLM_PROVIDER_AZURE
REFINE_SCRIPT_STRUCTURE_MODEL_KEY = MODEL_KEY_GPT41

REVIEW_SCRIPT_LANGUAGE_LLM_TYPE = LLM_PROVIDER_AZURE
REVIEW_SCRIPT_LANGUAGE_MODEL_KEY = MODEL_KEY_GPT41

REFINE_SCRIPT_LANGUAGE_LLM_TYPE = LLM_PROVIDER_DEEPSEEK
REFINE_SCRIPT_LANGUAGE_MODEL_KEY = MODEL_KEY_ARK_DEEPSEEK_R1

# 或者重新加载（如果需要刷新配置）
# LLM_TYPE, MODEL_KEY = load_model_settings()

# 也可以指定自定义配置路径
# LLM_TYPE, MODEL_KEY = load_model_settings('path/to/custom/settings.yaml')

class StoryParameters:
    """故事参数管理类"""
    def __init__(self,
                 min_episodes: int = 12,
                 max_episodes: int = 24,
                 target_episode_length: int = 3000,
                 min_scenes_per_episode: int = 5,
                 max_scenes_per_episode: int = 8,
                 batch_size: int = 5):
        self.min_episodes = min_episodes
        self.max_episodes = max_episodes
        self.target_episode_length = target_episode_length
        self.min_scenes_per_episode = min_scenes_per_episode
        self.max_scenes_per_episode = max_scenes_per_episode
        self.batch_size = batch_size

def preprocess_chinese_text(text: str) -> str:
    """Preprocess Chinese text."""
    text = unicodedata.normalize('NFKC', text)
    text = re.sub(r'[「『]', '"', text)
    text = re.sub(r'[」』]', '"', text)
    text = re.sub(r'[—]{2,}', '——', text)
    text = re.sub(r'[.]{3,}|…{2,}', '……', text)
    text = re.sub(r'([。！？…）】」』\)])(?!\s)', r'\1\n', text)
    return text

def save_episode_scripts(episodes: List[Dict[str, Any]], output_dir: str) -> None:
    """Save episode scripts to JSON files."""
    try:
        os.makedirs(output_dir, exist_ok=True)
        for episode in episodes:
            episode_number = episode['episode_number']
            # 将 episode_number 转换为整数后再格式化
            episode_num_int = int(episode_number)
            output_path = os.path.join(output_dir, f"episode_{episode_num_int:02d}.json")
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(episode, f, ensure_ascii=False, indent=2)
            logger.info(f"Episode {episode_number} script saved to {output_path}")
    except Exception as e:
        logger.error(f"Error saving episode scripts: {e}")
        raise

def determine_total_episodes(
    group_summaries: Dict[str, Any],
    global_outline: Dict[str, Any],
    style: str,
    language: str,
    target_episodes: int
) -> Dict[str, Any]:
    """确定总集数和分配策略，使用两阶段处理方式"""
    try:
        logger.info("开始确定总集数和分配策略...")

        # 第一阶段：划分故事阶段
        logger.info("第1阶段: 将故事划分为主要阶段")
        phase_data = {
            "global_outline": global_outline,
            "group_summaries": group_summaries,
            "style": style,
            "language": language
        }

        # 调用模型获取阶段划分
        phase_allocation = call_llm_json_response(
            "smart_phase_allocation",
            phase_data,
            llm_type=SMART_PHASE_ALLOCATION_LLM_TYPE,
            model_key=SMART_PHASE_ALLOCATION_MODEL_KEY,
            using_cache=True,
            max_retries=MAX_RETRY_ATTEMPTS
        )

        if not phase_allocation or "phases" not in phase_allocation:
            logger.error("阶段划分失败，无法继续进行")
            raise ValueError("阶段划分失败，请检查模型响应")

        logger.info(f"成功将故事划分为 {phase_allocation.get('total_phases', 0)} 个阶段")

        # 如果开启调试，保存阶段划分结果
        if EPISODE_DEBUG:
            write_temp_file(
                content=phase_allocation,
                filename_prefix="phase_allocation",
                base_dir=ANIMATION_DRAMA_DIR
            )

        # 第二阶段：将每个阶段划分为具体集数
        logger.info("第2阶段: 将每个阶段细分为具体集数")

        all_episodes = []
        global_episode_counter = 1  # 全局集数计数器

        for phase in phase_allocation.get("phases", []):
            phase_number = phase.get("phase_number")
            phase_name = phase.get("phase_name", f"阶段{phase_number}")
            group_ids = phase.get("groups", [])

            logger.info(f"处理{phase_name}(阶段{phase_number})，包含组: {', '.join(group_ids)}")

            # 获取该阶段涉及的组摘要
            phase_groups = {
                group_id: group_summaries[group_id]
                for group_id in group_ids
                if group_id in group_summaries
            }

            # 准备第二阶段请求数据
            episode_data = {
                "global_outline": global_outline,
                "phase": phase,
                "phase_groups": phase_groups,
                "style": style,
                "language": language
            }

            # 调用模型获取该阶段的集数分配
            phase_episodes = call_llm_json_response(
                "smart_episode_allocation",
                episode_data,
                llm_type=SMART_EPISODE_ALLOCATION_LLM_TYPE,
                model_key=SMART_EPISODE_ALLOCATION_MODEL_KEY,
                using_cache=True,
                max_retries=MAX_RETRY_ATTEMPTS
            )

            if not phase_episodes or "episodes" not in phase_episodes:
                logger.warning(f"阶段{phase_number}的集数分配失败，跳过该阶段")
                continue

            # 更新全局集数编号
            for episode in phase_episodes["episodes"]:
                episode["episode_number"] = global_episode_counter
                global_episode_counter += 1
                all_episodes.append(episode)

            logger.info(f"阶段{phase_number}分配了 {len(phase_episodes.get('episodes', []))} 集")

        # 构建最终结果
        final_allocation = {
            "total_episodes": len(all_episodes),
            "episodes": all_episodes
        }

        logger.info(f"最终分配结果: 总共 {final_allocation['total_episodes']} 集")

        # 保存最终分配结果用于调试
        if EPISODE_DEBUG:
            write_temp_file(
                content=final_allocation,
                filename_prefix="final_episode_allocation",
                base_dir=ANIMATION_DRAMA_DIR
            )

        logger.debug(f"Generate episode allocation successfully.")

        return final_allocation

    except Exception as e:
        logger.error(f"确定总集数失败: {str(e)}")
        raise

def generate_episode_structure(
    episode_number: int,
    chapter_summaries: List[Dict[str, Any]],
    global_outline: Dict[str, Any],
    episode_allocation: Dict[str, Any],
    previous_episode_structure: Optional[Dict[str, Any]] = None,
    language: str = "Chinese"
) -> Optional[Dict[str, Any]]:
    """生成单集结构"""
    try:
        # 获取本集信息
        current_episode = next(
            (ep for ep in episode_allocation["episodes"]
             if ep["episode_number"] == episode_number),
            None
        )

        if not current_episode:
            logger.error(f"未找到第 {episode_number} 集的分配信息")
            return None

        # 从 episode_allocation 中获取主要情节和冲突
        main_plots = current_episode["summary"]["main_plots"]
        main_conflict = current_episode["summary"]["main_conflict"]

        # 准备输入数据
        input_data = {
            "episode_allocation": current_episode,
            "chapter_summaries": chapter_summaries,
            "global_outline": global_outline,
            "previous_episode_structure": previous_episode_structure or "",
            "language": language,
            "main_plots": main_plots,
            "main_conflict": main_conflict
        }

        # 调用 GPT 生成结构，暂时只验证核心字段
        response = call_llm_json_response(
            "generate_episode_structure",
            input_data,
            llm_type=GENERATE_EPISODE_STRUCTURE_LLM_TYPE,
            model_key=GENERATE_EPISODE_STRUCTURE_MODEL_KEY,
            using_cache=True,  # 禁用缓存以确保生成完整结构
            expected_fields=[
                # 第一层主要结构
                "episode_structure",
                # 第二层关键字段
                "episode_structure.main_plots",
                "episode_structure.acts",
            ]
        )

        if response:
            # 保存调试信息
            if EPISODE_DEBUG:
                write_temp_file(
                    content=response,
                    filename_prefix=f"episode_structure_{episode_number:02d}",
                    base_dir=ANIMATION_DRAMA_DIR
                )

            logger.info(f"生成第 {episode_number} 集结构成功")
            return response

        logger.error(f"生成第 {episode_number} 集结构失败：GPT 响应为空")
        return None

    except Exception as e:
        logger.error(f"生成第 {episode_number} 集结构时出错: {str(e)}")
        return None

def review_script_structure(
    episode_structure: Dict[str, Any],
    story_outline: Dict[str, Any],
    language: str = "Chinese",
    episode_number: Optional[int] = None,
    episode_allocation: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """评估剧集结构质量并返回详细反馈"""
    try:
        logger.debug("执行结构评审...")

        # 从 episode_allocation 中获取当前集的信息
        current_episode = next(
            (ep for ep in episode_allocation["episodes"]
             if ep["episode_number"] == episode_number),
            None
        ) if episode_allocation else None

        # 获取主要情节和冲突
        main_plots = current_episode["summary"]["main_plots"] if current_episode else []
        main_conflict = current_episode["summary"]["main_conflict"] if current_episode else ""

        data = {
            "episode_structure": episode_structure,
            "global_outline": story_outline,
            "episode_allocation": episode_allocation,
            "language": language,
            "episode_number": episode_number,
            "main_plots": main_plots,  # 新增
            "main_conflict": main_conflict  # 新增
        }

        response = call_llm_json_response(
            "review_script_structure",
            data,
            llm_type=REVIEW_SCRIPT_STRUCTURE_LLM_TYPE,
            model_key=REVIEW_SCRIPT_STRUCTURE_MODEL_KEY,
            using_cache=True
        )

        if not response:
            logger.error("剧本结构评估失败：空响应")
            return {}

        if EPISODE_DEBUG:
            write_temp_file(
                content=response,
                filename_prefix=f"structure_review_{episode_number:02d}",
                base_dir=ANIMATION_DRAMA_DIR
            )

        return response

    except Exception as e:
        logger.error(f"结构评审失败: {str(e)}")
        return {}

def refine_script_structure(
    episode_structure: Dict[str, Any],
    structure_reviews: Dict[str, Any],
    story_outline: Dict[str, Any],
    style: str,
    language: str,
    episode_number: Optional[int] = None,
    chapter_summaries: Optional[List[Dict[str, Any]]] = None,
    episode_allocation: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """根据结构评审反馈优化剧本结构"""
    try:
        # 从 episode_allocation 中获取当前集的信息
        current_episode = next(
            (ep for ep in episode_allocation["episodes"]
             if ep["episode_number"] == episode_number),
            None
        ) if episode_allocation else None

        # 获取主要情节和冲突
        main_plots = current_episode["summary"]["main_plots"] if current_episode else []
        main_conflict = current_episode["summary"]["main_conflict"] if current_episode else ""

        data = {
            "episode_structure": episode_structure,
            "review_feedback": structure_reviews,
            "episode_allocation": episode_allocation,
            "chapter_summaries": chapter_summaries,
            "global_outline": story_outline,
            "language": language,
            "main_plots": main_plots,
            "main_conflict": main_conflict
        }

        response = call_llm_json_response(
            "refine_script_structure",
            data,
            llm_type=REFINE_SCRIPT_STRUCTURE_LLM_TYPE,
            model_key=REFINE_SCRIPT_STRUCTURE_MODEL_KEY,
            using_cache=True
        )

        if not response:
            logger.error("结构优化失败：空响应")
            return episode_structure

        if EPISODE_DEBUG:
            write_temp_file(
                content=response,
                filename_prefix=f"structure_refined_{episode_number:02d}",
                base_dir=ANIMATION_DRAMA_DIR
            )

        return response.get("episode_structure", episode_structure)

    except Exception as e:
        logger.error(f"剧本结构优化失败: {str(e)}")
        return episode_structure

def generate_episode_full_script(
    episode_number: int,
    episode_structure: Dict[str, Any],
    story_outline: Dict[str, Any],
    previous_episode_script: Optional[Dict[str, Any]] = None,
    chapter_summaries: List[Dict[str, Any]] = None,
    style: str = "engaging",
    language: str = "Chinese",
    world_tone: str = "modern"
) -> Optional[str]:
    """生成单集完整自由文本格式剧本"""
    try:
        # 从 episode_structure 中获取主要冲突
        main_conflict = ""
        if isinstance(episode_structure, dict):
            if "episode_structure" in episode_structure:
                main_conflict = episode_structure["episode_structure"].get("main_conflict", "")
            else:
                main_conflict = episode_structure.get("main_conflict", "")

        # 构建请求数据
        request_data = {
            "episode_structure": episode_structure,
            "global_outline": story_outline,
            "previous_episode_script": previous_episode_script,
            "chapter_summaries": chapter_summaries,
            "style": style,
            "language": language,
            "world_tone": world_tone,
            "main_conflict": main_conflict
        }

        # 直接调用 call_llm 获取文本响应
        response = call_llm(
            api_function="generate_full_script",
            prompt_data=request_data,
            llm_type=GENERATE_EPISODE_SCRIPT_LLM_TYPE,
            model_key=GENERATE_EPISODE_SCRIPT_MODEL_KEY,
            using_cache=False
        )

        if response:
            if EPISODE_DEBUG:
                write_temp_file(
                    content=response,
                    filename_prefix=f"full_script_{episode_number:02d}_text",
                    base_dir=ANIMATION_DRAMA_DIR
                )
            logger.info(f"生成第 {episode_number} 集完整剧本文本成功")
            return response

        logger.error(f"生成第 {episode_number} 集完整剧本文本失败")
        return None

    except Exception as e:
        logger.error(f"生成完整剧本文本时出错: {str(e)}")
        return None

def review_full_script(
    full_script: str,
    episode_structure: Dict[str, Any],
    story_outline: Dict[str, Any],
    previous_episode_script: Optional[Dict[str, Any]] = None,
    chapter_summaries: List[Dict[str, Any]] = None,
    language: str = "Chinese",
    episode_number: int = 0
) -> Optional[Dict[str, Any]]:
    """评审完整自由文本格式剧本

    Args:
        full_script: 完整自由文本格式剧本
        episode_structure: 剧集结构
        story_outline: 全局故事大纲
        previous_episode_script: 上一集的剧本
        chapter_summaries: 章节摘要列表
        language: 语言
        episode_number: 集数

    Returns:
        评审结果，如果评审失败则返回空字典
    """
    max_retries = 2

    for attempt in range(max_retries):
        try:
            logger.debug(f"执行 review_full_script 命令...(尝试 {attempt+1}/{max_retries})")

            # 检查输入参数
            if not full_script or not full_script.strip():
                logger.error("剧本文本为空，无法评审")
                return {}

            # 从 episode_structure 中获取主要冲突
            main_conflict = ""
            if isinstance(episode_structure, dict):
                if "episode_structure" in episode_structure:
                    main_conflict = episode_structure["episode_structure"].get("main_conflict", "")
                else:
                    main_conflict = episode_structure.get("main_conflict", "")

            # 构建请求数据
            request_data = {
                "full_script": full_script,
                "episode_structure": episode_structure,
                "global_outline": story_outline,
                "previous_episode_structure": previous_episode_script,
                "chapter_summaries": chapter_summaries,
                "language": language,
                "main_conflict": main_conflict
            }

            # 调用 LLM 评审剧本
            response = call_llm_json_response(
                "review_full_script",
                request_data,
                llm_type=REVIEW_SCRIPT_LLM_TYPE,
                model_key=REVIEW_SCRIPT_MODEL_KEY,
                using_cache=False
            )

            if not response:
                logger.error(f"剧本评审失败：空响应 (尝试 {attempt+1})")
                if attempt < max_retries - 1:
                    logger.info("等待3秒后重试...")
                    time.sleep(3)
                    continue
                return {}

            if EPISODE_DEBUG:
                write_temp_file(
                    content=response,
                    filename_prefix=f"full_script_{episode_number:02d}_review",
                    base_dir=ANIMATION_DRAMA_DIR
                )
            logger.info(f"第 {episode_number} 集剧本评审完成")
            return response

        except Exception as e:
            logger.error(f"评审剧本时出错 (尝试 {attempt+1}): {str(e)}")
            if attempt < max_retries - 1:
                logger.info("等待3秒后重试...")
                time.sleep(3)
            else:
                logger.exception("详细错误信息")

    return {}

def refine_full_script(
    full_script: str,
    review_feedback: Dict[str, Any],
    episode_structure: Dict[str, Any],
    story_outline: Dict[str, Any],
    chapter_summaries: List[Dict[str, Any]],
    style: str = "engaging",
    language: str = "Chinese",
    world_tone: str = "modern",
    episode_number: int = 0
) -> Optional[str]:
    """根据评审反馈改进完整自由文本格式剧本"""
    try:
        # 从 episode_structure 中获取主要冲突
        main_conflict = ""
        if isinstance(episode_structure, dict):
            if "episode_structure" in episode_structure:
                main_conflict = episode_structure["episode_structure"].get("main_conflict", "")
            else:
                main_conflict = episode_structure.get("main_conflict", "")

        # 构建请求数据
        request_data = {
            "full_script": full_script,
            "review_feedback": review_feedback,
            "episode_structure": episode_structure,
            "global_outline": story_outline,
            "chapter_summaries": chapter_summaries,
            "style": style,
            "language": language,
            "world_tone": world_tone,
            "main_conflict": main_conflict
        }

        # 直接调用 call_llm 获取文本响应
        response = call_llm(
            api_function="refine_full_script",
            prompt_data=request_data,
            llm_type=REFINE_SCRIPT_LLM_TYPE,
            model_key=REFINE_SCRIPT_MODEL_KEY,
            using_cache=False
        )

        if response:
            if EPISODE_DEBUG:
                write_temp_file(
                    content=response,
                    filename_prefix=f"full_script_{episode_number:02d}_refined",
                    base_dir=ANIMATION_DRAMA_DIR
                )
            logger.info(f"第 {episode_number} 集剧本改进完成")
            return response

        logger.error(f"第 {episode_number} 集剧本改进失败")
        return full_script  # 如果失败，返回原始剧本

    except Exception as e:
        logger.error(f"改进剧本时出错: {str(e)}")
        return full_script  # 发生异常时返回原始剧本

def convert_script_to_json_by_code(
    script_text: str,
    episode_number: int
) -> Optional[Dict[str, Any]]:
    """使用代码将自由文本格式剧本转换为 JSON 结构

    Args:
        script_text: 自由文本格式剧本
        episode_number: 集数

    Returns:
        转换后的 JSON 结构，如果转换失败则返回 None
    """
    try:
        if not script_text or not script_text.strip():
            logger.error("剧本文本为空，无法转换为JSON")
            return None

        # 清理文本
        cleaned_text = script_text.replace("\r", "\n").replace("\u200b", "").strip()
        lines = [line.strip() for line in cleaned_text.split('\n') if line.strip()]

        # 初始化结果结构
        result = {
            "ep": {
                "ep_n": episode_number,
                "t": f"第{episode_number}集",
                "c": [],
                "scenes": []
            }
        }

        # 解析状态
        current_scene = None
        scene_counter = 1
        shot_counter = 1
        characters = set()

        # 正则表达式模式
        scene_pattern = re.compile(r'^(?:场景|SCENE|Scene)\s*(\d+)?[：:]?\s*(.*)', re.IGNORECASE)
        character_dialogue_pattern = re.compile(r'^([^：:]+)[：:]\s*(.+)')
        narration_pattern = re.compile(r'^\[([^\]]+)\]|^（([^）]+)）|^旁白[：:]?\s*(.+)', re.IGNORECASE)
        environment_pattern = re.compile(r'^(?:环境|背景|场景描述)[：:]?\s*(.+)', re.IGNORECASE)

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测场景标记
            scene_match = scene_pattern.match(line)
            if scene_match:
                # 保存当前场景
                if current_scene:
                    result["ep"]["scenes"].append(current_scene)

                # 创建新场景
                scene_description = scene_match.group(2) if scene_match.group(2) else "未指定场景"
                current_scene = {
                    "scene_number": scene_counter,
                    "n": scene_counter,
                    "sn": shot_counter,
                    "shot_type": "establishing",
                    "environment": {"image": scene_description},
                    "narration": {"nr": ""},
                    "dialogue": [],
                    "sound_cues": []
                }
                scene_counter += 1
                shot_counter += 1
                continue

            # 如果还没有场景，创建默认场景
            if current_scene is None:
                current_scene = {
                    "scene_number": scene_counter,
                    "n": scene_counter,
                    "sn": shot_counter,
                    "shot_type": "dialogue",
                    "environment": {"image": "未指定环境"},
                    "narration": {"nr": ""},
                    "dialogue": [],
                    "sound_cues": []
                }
                scene_counter += 1
                shot_counter += 1

            # 检测环境描述
            env_match = environment_pattern.match(line)
            if env_match:
                current_scene["environment"]["image"] = env_match.group(1)
                continue

            # 检测旁白
            narration_match = narration_pattern.match(line)
            if narration_match:
                narration_text = narration_match.group(1) or narration_match.group(2) or narration_match.group(3)
                if current_scene["narration"]["nr"]:
                    current_scene["narration"]["nr"] += " " + narration_text
                else:
                    current_scene["narration"]["nr"] = narration_text
                continue

            # 检测角色对话
            dialogue_match = character_dialogue_pattern.match(line)
            if dialogue_match:
                character_name = dialogue_match.group(1).strip()
                dialogue_text = dialogue_match.group(2).strip()

                # 添加角色到角色列表
                characters.add(character_name)

                # 简单的情绪检测
                mood = "Neutral"
                if any(word in dialogue_text for word in ["！", "!", "？？", "??"]):
                    mood = "Angry"
                elif any(word in dialogue_text for word in ["？", "?"]):
                    mood = "Nervous"
                elif any(word in dialogue_text for word in ["哈哈", "呵呵", "笑"]):
                    mood = "Cheerful"
                elif any(word in dialogue_text for word in ["唉", "哎", "悲"]):
                    mood = "Sad"

                current_scene["dialogue"].append({
                    "c": character_name,
                    "m": mood,
                    "t": dialogue_text
                })
                current_scene["shot_type"] = "dialogue"
                continue

            # 其他文本作为旁白处理
            if current_scene["narration"]["nr"]:
                current_scene["narration"]["nr"] += " " + line
            else:
                current_scene["narration"]["nr"] = line

        # 保存最后一个场景
        if current_scene:
            result["ep"]["scenes"].append(current_scene)

        # 生成角色列表
        for char_name in characters:
            # 简单的性别和年龄推断
            gender = "male"
            age = "Adult"

            # 基于常见中文名字模式的简单推断
            if any(char in char_name for char in ["女", "妹", "姐", "母", "婆", "娘"]):
                gender = "female"
            elif any(char in char_name for char in ["小", "少"]):
                age = "Young"
            elif any(char in char_name for char in ["老", "长", "爷"]):
                age = "Senior"

            result["ep"]["c"].append({
                "name": char_name,
                "gender": gender,
                "age": age,
                "role": ["主要角色"],
                "aliases": []
            })

        # 验证结果
        if not result["ep"]["scenes"]:
            logger.warning("未检测到任何场景，创建默认场景")
            result["ep"]["scenes"].append({
                "scene_number": 1,
                "n": 1,
                "sn": 1,
                "shot_type": "dialogue",
                "environment": {"image": "未指定环境"},
                "narration": {"nr": cleaned_text[:200] + "..." if len(cleaned_text) > 200 else cleaned_text},
                "dialogue": [],
                "sound_cues": []
            })

        if EPISODE_DEBUG:
            write_temp_file(
                content=result,
                filename_prefix=f"full_script_{episode_number:02d}_json_code",
                base_dir=ANIMATION_DRAMA_DIR
            )

        logger.info(f"第 {episode_number} 集剧本代码转换为 JSON 成功，共 {len(result['ep']['scenes'])} 个场景")
        return result

    except Exception as e:
        logger.error(f"代码转换剧本为 JSON 时出错: {str(e)}")
        logger.exception("详细错误信息")
        return None

def convert_script_to_json(
    script_text: str,
    episode_number: int
) -> Optional[Dict[str, Any]]:
    """将自由文本格式剧本转换为 JSON 结构

    Args:
        script_text: 自由文本格式剧本
        episode_number: 集数

    Returns:
        转换后的 JSON 结构，如果转换失败则返回 None

    示例输出结构:
    {
        "ep": {
            "ep_n": "integer",           // episode number
            "t": "string",            // title
            "c": [                    // characters
                {
                    "name": "string", // Character name
                    "gender": "string", // male/female
                    "age": "string",    // Adult/Young/Child/Senior
                    "role": ["string"], // role
                    "aliases": ["alias1", "alias2"]
                }
            ],
            "scenes": [                    // scenes (ordered sequentially)
                {
                    "scene_number": "integer",  // marker for scene grouping
                    "n": "integer",   // shot number within scene
                    "sn": "integer",  // overall sequence number
                    "shot_type": "string",  // e.g., "establishing", "conflict", "emotional", "action", "transition", "dialogue"
                    "environment": {
                        "image": "string"    // 20-30 words, static description
                    },
                    "narration": {
                        "nr": "string"       // up to 80 words narration
                    },
                    "dialogue": [
                        {
                            "c": "string",      // character name
                            "m": "string",      // mood: Neutral/Cheerful/Sad/Angry/Nervous
                            "t": "string"       // dialogue text
                        }
                    ],
                    "sound_cues": [ "string" ]  // Optional sound effects
                }
            ]
        }
    }
    """
    try:
        # 根据配置选择转换方法
        if USE_CODE_CONVERSION:
            logger.info(f"使用代码转换第 {episode_number} 集剧本为 JSON")
            return convert_script_to_json_by_code(script_text, episode_number)
        else:
            logger.info(f"使用 LLM 转换第 {episode_number} 集剧本为 JSON")
            return convert_script_to_json_by_llm(script_text, episode_number)

    except Exception as e:
        logger.error(f"转换剧本为 JSON 时出错: {str(e)}")
        logger.exception("详细错误信息")
        return None

def convert_script_to_json_by_llm(
    script_text: str,
    episode_number: int
) -> Optional[Dict[str, Any]]:
    """使用 LLM 将自由文本格式剧本转换为 JSON 结构"""
    try:
        # 检查输入是否为空
        if not script_text or not script_text.strip():
            logger.error("剧本文本为空，无法转换为JSON")
            return None

        # 处理可能的格式错误
        cleaned_text = script_text.replace("\r", "\n").replace("\u200b", "").strip()

        # 直接重试机制
        max_retries = 2
        for retry in range(max_retries):
            try:
                # 构建请求数据
                request_data = {
                    "script_text": cleaned_text,
                    "episode_number": episode_number  # 确保传递集数
                }

                # 调用 LLM 转换剧本
                response = call_llm_json_response(
                    "convert_script_to_json",
                    request_data,
                    llm_type=GENERATE_EPISODE_SCRIPT_LLM_TYPE,
                    model_key=GENERATE_EPISODE_SCRIPT_MODEL_KEY,
                    using_cache=False
                )

                # 验证响应
                if not response:
                    logger.error(f"第 {episode_number} 集剧本转换为 JSON 失败: 模型响应为空")
                    continue

                #logger.debug(f"第 {episode_number} 集剧本转换为 JSON: \n {response} \n")

                # 检查响应格式
                if "ep" not in response:
                    logger.error(f"第 {episode_number} 集剧本转换为 JSON 失败: 响应中缺少 'ep' 字段")
                    # 尝试修复响应
                    if isinstance(response, dict):
                        # 如果顶层就包含了expected fields，将其封装到 ep 中
                        if "scenes" in response or "title" in response:
                            fixed_response = {"ep": response}
                            response = fixed_response
                            logger.warning("已尝试修复缺少 'ep' 字段的响应")
                        else:
                            continue
                    else:
                        continue

                # 验证和修复剧本数据 - 这里返回整个response，不再提取ep
                # 确保集数正确
                if "ep_n" not in response["ep"]:
                    response["ep"]["ep_n"] = episode_number
                else:
                    response["ep"]["ep_n"] = episode_number  # 总是使用传入的集数

                # 验证必要字段
                if "scenes" not in response["ep"]:
                    logger.error("转换后的剧本缺少场景数据")
                    continue

                # 验证每个场景的必要字段
                for i, scene in enumerate(response["ep"]["scenes"]):
                    # 基本场景编号和序列编号
                    if "scene_number" not in scene:
                        scene["scene_number"] = i + 1
                    if "n" not in scene:
                        scene["n"] = i + 1  # 场景内编号
                    if "sn" not in scene:
                        scene["sn"] = i + 1  # 总体序列编号

                    # 场景类型
                    if "shot_type" not in scene:
                        scene["shot_type"] = "dialogue"  # 默认为对话类型

                    # 环境描述
                    if "environment" not in scene:
                        scene["environment"] = {"image": "未指定环境描述"}
                    elif isinstance(scene["environment"], dict) and "image" not in scene["environment"]:
                        scene["environment"]["image"] = "未指定环境描述"

                    # 旁白部分
                    if "narration" not in scene:
                        scene["narration"] = {"nr": "未指定旁白"}
                    elif isinstance(scene["narration"], dict) and "nr" not in scene["narration"]:
                        scene["narration"]["nr"] = "未指定旁白"

                    # 对话部分
                    if "dialogue" not in scene:
                        scene["dialogue"] = []
                    elif not isinstance(scene["dialogue"], list):
                        # 如果dialogue不是列表，尝试转换
                        scene["dialogue"] = [{"c": "未知角色", "m": "Neutral", "t": str(scene["dialogue"])}]

                    # 音效提示（可选）
                    if "sound_cues" not in scene:
                        scene["sound_cues"] = []

                if EPISODE_DEBUG:
                    write_temp_file(
                        content=response,
                        filename_prefix=f"full_script_{episode_number:02d}_json",
                        base_dir=ANIMATION_DRAMA_DIR
                    )
                logger.info(f"第 {episode_number} 集剧本转换为 JSON 成功")
                return response  # 返回整个response，包含ep键

            except Exception as e:
                logger.error(f"第 {episode_number} 集剧本转换为 JSON 尝试 {retry+1} 失败: {str(e)}")
                if retry == max_retries - 1:
                    logger.error("达到最大重试次数，转换失败")
                else:
                    logger.info(f"5秒后尝试重新转换...")
                    time.sleep(5)  # 稍等一会再重试

        logger.error(f"第 {episode_number} 集剧本转换为 JSON 失败，所有尝试均失败")
        return None

    except Exception as e:
        logger.error(f"转换剧本为 JSON 时出错: {str(e)}")
        logger.exception("详细错误信息")
        return None

def generate_episode_script(
    episode_number: int,
    episode_structure: Dict[str, Any],
    story_outline: Dict[str, Any],
    previous_episode_script: Optional[Dict[str, Any]] = None,
    world_setting: Optional[Dict[str, Any]] = None,
    style: str = "engaging",
    language: str = "Chinese",
    world_tone: str = "modern",
    relevant_context: Optional[str] = None,
    chapter_summaries: List[Dict[str, Any]] = None
) -> Optional[Dict[str, Any]]:
    """生成单集剧本，使用分步流程：生成自由文本 -> 评审 -> 改进 -> 转换为 JSON

    Args:
        episode_number: 集数
        episode_structure: 剧集结构，包含场景、人物、情节等详细信息
        story_outline: 全局故事大纲
        previous_episode_script: 上一集的完整剧本
        world_setting: 世界观设定
        style: 风格
        language: 语言
        world_tone: 世界基调
        relevant_context: 相关上下文
        chapter_summaries: 章节摘要列表

    Returns:
        Optional[Dict[str, Any]]: 生成的剧本，如果生成失败则返回None
    """
    max_attempts = 2  # 每个步骤的最大尝试次数

    try:
        logger.info(f"开始为第 {episode_number} 集生成剧本（分步流程）")

        # 验证必要的输入
        if not episode_structure:
            logger.error("缺少必要的剧集结构信息")
            return None

        if not story_outline:
            logger.error("缺少必要的故事大纲信息")
            return None

        if not chapter_summaries:
            logger.warning("缺少章节摘要信息，可能会影响剧本质量")

        # 步骤 1: 生成完整自由文本格式剧本
        full_script = None
        for attempt in range(max_attempts):
            try:
                logger.info(f"步骤1: 为第 {episode_number} 集生成自由文本剧本 (尝试 {attempt + 1}/{max_attempts})")
                full_script = generate_episode_full_script(
                    episode_number=episode_number,
                    episode_structure=episode_structure,
                    story_outline=story_outline,
                    previous_episode_script=previous_episode_script,
                    chapter_summaries=chapter_summaries,
                    style=style,
                    language=language,
                    world_tone=world_tone
                )
                if full_script:
                    break
            except Exception as e:
                logger.error(f"自由文本剧本生成失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == max_attempts - 1:
                    logger.error("达到最大尝试次数，剧本生成失败")
                    return None
                time.sleep(2)  # 短暂延迟后重试

        if not full_script:
            logger.error(f"第 {episode_number} 集自由文本剧本生成失败")
            return None

        # 验证生成的文本是否达到最小长度要求
        if len(full_script.split()) < EPISODE_SCRIPT_MIN_WORDS:
            logger.warning(f"生成的剧本文本长度不足 ({len(full_script.split())} 词)，但会继续处理")

        # 步骤 2: 评审剧本
        review_feedback = None
        for attempt in range(max_attempts):
            try:
                logger.info(f"步骤2: 评审第 {episode_number} 集剧本 (尝试 {attempt + 1}/{max_attempts})")
                review_feedback = review_full_script(
                    full_script=full_script,
                    episode_structure=episode_structure,
                    story_outline=story_outline,
                    previous_episode_script=previous_episode_script,
                    chapter_summaries=chapter_summaries,
                    language=language,
                    episode_number=episode_number
                )
                if review_feedback:
                    break
            except Exception as e:
                logger.error(f"剧本评审失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == max_attempts - 1:
                    logger.warning("达到最大尝试次数，使用空评审结果继续")
                    review_feedback = {}
                time.sleep(2)

        # 确保评审结果是一个字典
        if not review_feedback:
            review_feedback = {}

        # 步骤 3: 根据评审反馈改进剧本
        refined_script = None
        for attempt in range(max_attempts):
            try:
                logger.info(f"步骤3: 根据评审改进第 {episode_number} 集剧本 (尝试 {attempt + 1}/{max_attempts})")
                refined_script = refine_full_script(
                    full_script=full_script,
                    review_feedback=review_feedback,
                    episode_structure=episode_structure,
                    story_outline=story_outline,
                    chapter_summaries=chapter_summaries,
                    style=style,
                    language=language,
                    world_tone=world_tone,
                    episode_number=episode_number
                )
                if refined_script:
                    break
            except Exception as e:
                logger.error(f"剧本改进失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == max_attempts - 1:
                    logger.warning("达到最大尝试次数，使用原始剧本继续")
                    refined_script = full_script
                time.sleep(2)

        # 如果无法获得改进的剧本，则使用原始剧本
        if not refined_script:
            logger.warning(f"第 {episode_number} 集剧本改进失败，使用原始剧本")
            refined_script = full_script

        # 步骤 4: 将自由文本剧本转换为 JSON 结构
        final_script = None
        for attempt in range(max_attempts):
            try:
                logger.info(f"步骤4: 将第 {episode_number} 集剧本转换为JSON (尝试 {attempt + 1}/{max_attempts})")
                final_script = convert_script_to_json(
                    script_text=refined_script,
                    episode_number=episode_number
                )
                if final_script:
                    break
            except Exception as e:
                logger.error(f"剧本JSON转换失败 (尝试 {attempt + 1}): {str(e)}")
                if attempt == max_attempts - 1:
                    logger.error("达到最大尝试次数，剧本JSON转换失败")
                    return None
                time.sleep(2)

        if not final_script:
            logger.error(f"第 {episode_number} 集剧本 JSON 转换失败")
            return None

        # 步骤 5: 增强角色心理深度和场景描述
        try:
            logger.info(f"步骤5: 增强第 {episode_number} 集的角色心理深度和场景描述")

            # 提取角色信息进行心理分析
            characters_in_episode = {}
            if "ep" in final_script and "c" in final_script["ep"]:
                for char in final_script["ep"]["c"]:
                    char_name = char.get("name", "")
                    if char_name:
                        characters_in_episode[char_name] = char

            # 分析每个角色的心理状态
            character_psychologies = {}
            for char_name, char_info in characters_in_episode.items():
                try:
                    psychology = analyze_character_psychology(
                        character_name=char_name,
                        character_info=char_info,
                        story_context=story_outline,
                        current_situation=f"第{episode_number}集的情节发展"
                    )
                    if psychology:
                        character_psychologies[char_name] = psychology
                except Exception as e:
                    logger.warning(f"角色 {char_name} 心理分析失败: {str(e)}")

            # 增强场景描述和对话
            if "ep" in final_script and "scenes" in final_script["ep"]:
                scenes = final_script["ep"]["scenes"]

                # 构建情感弧线
                episode_emotional_arc = {
                    "opening_emotion": "紧张与困惑",
                    "midpoint_emotion": "冲突与抉择",
                    "climax_emotion": "决心与行动",
                    "resolution_emotion": "希望与转变"
                }

                # 增强场景描述
                enhanced_scenes = enhance_scene_descriptions(
                    scenes=scenes,
                    episode_emotional_arc=episode_emotional_arc,
                    character_states=character_psychologies
                )

                if enhanced_scenes:
                    final_script["ep"]["scenes"] = enhanced_scenes

                # 增强对话的心理深度
                for scene in final_script["ep"]["scenes"]:
                    if "dialogue" in scene and scene["dialogue"]:
                        try:
                            scene_context = scene.get("narration", {}).get("nr", "")
                            enhanced_dialogue = enhance_dialogue_with_psychology(
                                dialogue_list=scene["dialogue"],
                                character_psychologies=character_psychologies,
                                scene_emotional_context=scene_context
                            )
                            if enhanced_dialogue:
                                scene["dialogue"] = enhanced_dialogue
                        except Exception as e:
                            logger.warning(f"场景对话增强失败: {str(e)}")

            logger.info(f"第 {episode_number} 集心理深度和场景增强完成")

        except Exception as e:
            logger.warning(f"第 {episode_number} 集增强处理失败，使用原始剧本: {str(e)}")

        # 验证最终剧本的必要字段
        if "ep" in final_script:
            required_fields = ["ep_n", "scenes"]
            for field in required_fields:
                if field not in final_script["ep"]:
                    logger.warning(f"最终剧本缺少必要字段: ep.{field}，尝试添加")
                    if field == "ep_n":
                        final_script["ep"]["ep_n"] = episode_number
        else:
            logger.error("最终剧本缺少 'ep' 顶层字段")
            return None

        # 保存结构信息到最终剧本
        if "episode_structure" not in final_script:
            final_script["episode_structure"] = episode_structure

        logger.info(f"成功完成第 {episode_number} 集剧本生成流程")
        return final_script

    except Exception as e:
        logger.error(f"生成第 {episode_number} 集剧本时出错: {str(e)}")
        logger.exception("详细异常堆栈")
        return None

def process_novel(input_file: str, output_dir: str, style: str, language: str, max_episodes: Optional[int] = None) -> None:
    """处理小说生成剧本的主函数

    Args:
        input_file: 章节摘要JSON文件的路径
        output_dir: 保存分集剧本的目录
        style: 剧本风格
        language: 语言
        max_episodes: 最大生成集数（用于测试）

    Returns:
        None

    Raises:
        ValueError: 当关键步骤失败时抛出，例如无法生成故事大纲
        FileNotFoundError: 当输入文件不存在时抛出
        Exception: 处理过程中的其他错误
    """
    try:
        # 检查输入文件是否存在
        if not os.path.exists(input_file):
            raise FileNotFoundError(f"输入文件不存在: {input_file}")

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(ANIMATION_OUTPUTS_DIR, exist_ok=True)

        # 获取输入文件的前缀，用于保存中间结果
        input_basename = os.path.splitext(os.path.basename(input_file))[0]

        logger.info(f"开始处理小说: {input_basename}")

        # 1. 加载章节摘要
        logger.info("步骤1: 加载章节摘要")
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                chapter_summaries = json.load(f)
            logger.info(f"成功加载 {len(chapter_summaries)} 个章节摘要")
        except json.JSONDecodeError:
            raise ValueError(f"无法解析JSON文件: {input_file}, 请确保文件格式正确")

        # 2. 生成分组摘要并保存
        logger.info("步骤2: 生成分组摘要")
        groups = group_chapter_summaries(chapter_summaries)
        group_summaries = summarize_groups(groups, style, language)
        group_output = os.path.join(ANIMATION_OUTPUTS_DIR, f"{input_basename}_group.json")
        with open(group_output, 'w', encoding='utf-8') as f:
            json.dump(group_summaries, f, ensure_ascii=False, indent=2)
        logger.info(f"分组摘要已保存至: {group_output}")

        # 3. 生成并保存故事大纲
        logger.info("步骤3: 生成故事大纲")
        story_outline = generate_global_outline(
            group_summaries,
            style,
            language,
            input_file
        )
        if not story_outline:
            raise ValueError("故事大纲生成失败")

        outline_output = os.path.join(ANIMATION_OUTPUTS_DIR, f"{input_basename}_story_outline.json")
        with open(outline_output, 'w', encoding='utf-8') as f:
            json.dump(story_outline, f, ensure_ascii=False, indent=2)
        logger.info(f"故事大纲已保存至: {outline_output}")

        # 4. 确定总集数和分配并保存
        logger.info("步骤4: 确定总集数和分配")
        episode_allocation = determine_total_episodes(
            group_summaries,
            story_outline,
            style,
            language,
            target_episodes=60
        )

        # 记录总集数信息
        total_episodes = episode_allocation.get("total_episodes", 0)
        logger.info(f"确定总集数: {total_episodes}")

        allocation_output = os.path.join(ANIMATION_OUTPUTS_DIR, f"{input_basename}_episode_allocation.json")
        with open(allocation_output, 'w', encoding='utf-8') as f:
            json.dump(episode_allocation, f, ensure_ascii=False, indent=2)
        logger.info(f"集数分配已保存至: {allocation_output}")

        # 5. 生成每集内容
        logger.info("步骤5: 生成每集内容")
        previous_episode_script = None
        previous_episode_structure = None

        # 创建进度条
        total_to_process = len(episode_allocation["episodes"])
        if DEBUG_MODE:
            total_to_process = min(total_to_process, DEBUG_MAX_EPISODES)
        if max_episodes:
            total_to_process = min(total_to_process, max_episodes)

        with tqdm(total=total_to_process, desc="生成剧集") as pbar:
            for episode in episode_allocation["episodes"]:
                episode_number = episode["episode_number"]

                if DEBUG_MODE and episode_number > DEBUG_MAX_EPISODES:
                    logger.info(f"调试模式：已达到最大集数 {DEBUG_MAX_EPISODES}，停止生成")
                    break

                if max_episodes and episode_number > max_episodes:
                    logger.info(f"已达到最大集数 {max_episodes}，停止生成")
                    break

                logger.info(f"--- 开始处理第 {episode_number} 集 ---")

                # 检查是否已存在该集的剧本
                if ENABLE_SCRIPT_CACHE and check_existing_episode(output_dir, episode_number):
                    logger.info(f"使用缓存的第 {episode_number} 集剧本")
                    previous_episode_script = load_cached_episode(output_dir, episode_number)
                    # 从缓存的剧本中获取结构信息
                    previous_episode_structure = previous_episode_script.get('episode_structure', None) if previous_episode_script else None
                    pbar.update(1)
                    continue

                try:
                    # 获取本集对应的章节
                    episode_chapters = episode.get("chapters", [])
                    if not episode_chapters:
                        logger.warning(f"第 {episode_number} 集没有对应的章节，跳过生成")
                        pbar.update(1)
                        continue

                    # 获取对应的章节摘要
                    episode_chapter_summaries = [
                        summary for summary in chapter_summaries
                        if summary["basic_info"]["chapter_number"] in episode_chapters
                    ]

                    if not episode_chapter_summaries:
                        logger.warning(f"无法找到第 {episode_number} 集的章节摘要，跳过生成")
                        pbar.update(1)
                        continue

                    # 生成单集大纲和结构
                    logger.info(f"为第 {episode_number} 集生成结构")
                    episode_structure = generate_episode_structure(
                        episode_number=episode_number,
                        chapter_summaries=episode_chapter_summaries,
                        global_outline=story_outline,
                        episode_allocation=episode_allocation,
                        previous_episode_structure=previous_episode_structure,
                        language=language,
                    )

                    if not episode_structure:
                        logger.error(f"第 {episode_number} 集结构生成失败，跳过")
                        pbar.update(1)
                        continue

                    # 新增：结构评审和优化
                    logger.info(f"评审第 {episode_number} 集结构")
                    structure_reviews = review_script_structure(
                        episode_structure=episode_structure,
                        story_outline=story_outline,
                        language=language,
                        episode_number=episode_number,
                        episode_allocation=episode_allocation
                    )

                    # 根据结构评审优化
                    logger.info(f"优化第 {episode_number} 集结构")
                    refined_structure = refine_script_structure(
                        episode_structure=episode_structure,
                        structure_reviews=structure_reviews,
                        story_outline=story_outline,
                        style=style,
                        language=language,
                        episode_number=episode_number,
                        chapter_summaries=episode_chapter_summaries,
                        episode_allocation=episode_allocation
                    )

                    # 使用优化后的结构生成剧本
                    logger.info(f"为第 {episode_number} 集生成剧本")
                    episode_script = generate_episode_script(
                        episode_number=episode_number,
                        episode_structure=refined_structure,  # 使用优化后的结构
                        story_outline=story_outline,
                        previous_episode_script=previous_episode_script or {},
                        world_setting=story_outline.get("world_setting", {}),
                        style=style,
                        language=language,
                        world_tone=story_outline.get("tone", "modern"),
                        relevant_context=episode.get("context", ""),
                        chapter_summaries=episode_chapter_summaries,
                    )

                    if not episode_script:
                        logger.error(f"第 {episode_number} 集剧本生成失败，跳过")
                        pbar.update(1)
                        continue

                    # 保存最终优化后的剧本
                    save_single_episode(
                        episode_script,
                        output_dir,
                        episode_number
                    )

                    previous_episode_script = episode_script
                    previous_episode_structure = refined_structure

                    logger.info(f"第 {episode_number} 集剧本生成完成")

                except Exception as e:
                    logger.error(f"处理第 {episode_number} 集时发生错误: {str(e)}")
                    logger.exception("详细错误信息")

                pbar.update(1)

                # 每处理完一定数量的集数后保存进度
                if episode_number % PROGRESS_SAVE_INTERVAL_CHAPTERS == 0:
                    logger.info(f"已完成 {episode_number} 集的生成, 占总体进度 {episode_number/total_episodes*100:.1f}%")

        logger.info(f"所有剧本生成完成，共 {total_episodes} 集，已保存到 {output_dir}")

    except FileNotFoundError as e:
        logger.error(f"文件未找到: {str(e)}")
        raise
    except ValueError as e:
        logger.error(f"处理失败 - 值错误: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"处理失败 - 未预期错误: {str(e)}")
        logger.exception("详细错误信息")
        raise

def check_existing_episode(output_dir: str, episode_number: int) -> bool:
    """检查特定集数的剧本是否存在。"""
    episode_path = os.path.join(output_dir, f"episode_{int(episode_number):02d}.json")
    return os.path.exists(episode_path)

def save_single_episode(script: Dict[str, Any], output_dir: str, episode_number: int) -> None:
    """保存集剧本到文件"""
    try:
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f"episode_{episode_number:02d}.json")

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(script, f, ensure_ascii=False, indent=2)

        logger.info(f"Episode {episode_number} script saved to {output_path}")

    except Exception as e:
        logger.error(f"Error saving episode script: {e}")
        raise

def load_cached_episode(output_dir: str, episode_number: int) -> Dict[str, Any]:
    """从缓存中加载特定集数的脚本"""
    try:
        episode_path = os.path.join(output_dir, f"episode_{int(episode_number):02d}.json")
        if os.path.exists(episode_path):
            with open(episode_path, 'r', encoding='utf-8') as f:
                cached_episode = json.load(f)
            logger.info(f"从缓存加载了第 {episode_number} 集的剧集脚本")
            return cached_episode
        else:
            logger.warning(f"缓存中未找到第 {episode_number} 集的剧集脚本")
            return {}
    except Exception as e:
        logger.error(f"加载缓存的剧集脚本时错: {e}")
        raise

def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='从章节摘要生成分集剧本')
    parser.add_argument('input_file', help='章节摘要JSON文件的路径')

    # 修改 output_dir 的默认值处理
    parser.add_argument('--output_dir', help='保存分集剧本的目录', default=None)
    parser.add_argument('--style', default=get_param('STYLE', 'engaging'), help='剧本风格')
    parser.add_argument('--language', default=get_param('LANGUAGE', 'Chinese'), help='语言')
    parser.add_argument('--max_episodes', type=int, default=None, help='最大生成集数（用于测试）')

    args = parser.parse_args()

    # 如果没有指定输出目录，则使用配置中的默认路径
    if args.output_dir is None:
        input_basename = os.path.splitext(os.path.basename(args.input_file))[0]
        args.output_dir = os.path.join(ANIMATION_EPISODES_DIR, input_basename)

    return args

def main():
    """Main function."""
    args = parse_arguments()

    # 模型配置已在模块顶部通过 load_model_settings() 加载
    logger.info(f"使用 LLM 提供商: {LLM_TYPE}, 模型: {MODEL_KEY}")

    process_novel(
        input_file=args.input_file,
        output_dir=args.output_dir,
        style=args.style,
        language=args.language,
        max_episodes=args.max_episodes
    )

def analyze_character_psychology(
    character_name: str,
    character_info: Dict[str, Any],
    story_context: Dict[str, Any],
    current_situation: str
) -> Dict[str, Any]:
    """深度分析角色心理状态和动机"""
    try:
        analysis_data = {
            "character_name": character_name,
            "character_background": character_info,
            "story_context": story_context,
            "current_situation": current_situation,
            "psychology_framework": CHARACTER_PSYCHOLOGY_FRAMEWORK
        }

        # 调用LLM进行心理分析
        psychology_analysis = call_llm_json_response(
            "analyze_character_psychology",
            analysis_data,
            llm_type=LLM_TYPE,
            model_key=MODEL_KEY,
            using_cache=True
        )

        return psychology_analysis or {}

    except Exception as e:
        logger.error(f"角色心理分析失败: {str(e)}")
        return {}

def enhance_dialogue_with_psychology(
    dialogue_list: List[Dict[str, Any]],
    character_psychologies: Dict[str, Any],
    scene_emotional_context: str
) -> List[Dict[str, Any]]:
    """基于心理分析增强对话的深度和真实性"""
    try:
        enhancement_data = {
            "original_dialogues": dialogue_list,
            "character_psychologies": character_psychologies,
            "scene_context": scene_emotional_context,
            "enhancement_guidelines": CHARACTER_PSYCHOLOGY_FRAMEWORK
        }

        enhanced_dialogues = call_llm_json_response(
            "enhance_dialogue_psychology",
            enhancement_data,
            llm_type=LLM_TYPE,
            model_key=MODEL_KEY,
            using_cache=False
        )

        return enhanced_dialogues.get("enhanced_dialogues", dialogue_list)

    except Exception as e:
        logger.error(f"对话心理增强失败: {str(e)}")
        return dialogue_list

def enhance_scene_descriptions(
    scenes: List[Dict[str, Any]],
    episode_emotional_arc: Dict[str, Any],
    character_states: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """增强场景描述的感官细节和情感深度"""
    try:
        enhancement_data = {
            "original_scenes": scenes,
            "emotional_arc": episode_emotional_arc,
            "character_emotional_states": character_states,
            "description_framework": SCENE_DESCRIPTION_FRAMEWORK,
            "quality_standards": EPISODE_QUALITY_STANDARDS
        }

        enhanced_scenes = call_llm_json_response(
            "enhance_scene_descriptions",
            enhancement_data,
            llm_type=LLM_TYPE,
            model_key=MODEL_KEY,
            using_cache=False
        )

        return enhanced_scenes.get("enhanced_scenes", scenes)

    except Exception as e:
        logger.error(f"场景描述增强失败: {str(e)}")
        return scenes

def test_script_conversion():
    """测试脚本转换功能"""
    test_script = """
场景1：古老的图书馆
环境：昏暗的图书馆内，书架高耸，灰尘在微弱的光线中飞舞。

[旁白] 在这个被遗忘的角落，古老的秘密即将被揭开。

罗兰：这里的书籍都有几百年的历史了。
艾莉娅：你确定我们能在这里找到答案吗？
罗兰：相信我，答案就在这些古老的文字中。

场景2：密室发现
环境：一个隐藏的密室，墙上刻满了神秘的符文。

艾莉娅：天哪！这些符文...它们在发光！
罗兰：我们找到了！这就是传说中的预言之室。

[旁白] 两人的命运从此刻开始改变。
    """

    print("=== 测试代码转换 ===")
    result_code = convert_script_to_json_by_code(test_script, 1)
    if result_code:
        print("代码转换成功！")
        print(f"场景数量: {len(result_code['ep']['scenes'])}")
        print(f"角色数量: {len(result_code['ep']['c'])}")
    else:
        print("代码转换失败！")

    return result_code

if __name__ == "__main__":
    # 如果需要测试转换功能，取消下面的注释
    # test_script_conversion()

    main()